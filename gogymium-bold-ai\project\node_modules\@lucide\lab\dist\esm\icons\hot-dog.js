/**
 * @license @lucide/lab v0.1.2 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

const hotDog = [
  ["path", { d: "M17.1 3.5a4 4 0 0 0-5.9-.3l-8 8a4 4 0 0 0 .2 5.9", key: "1yxrxa" }],
  ["path", { d: "M6.9 20.7a4.07 4.07 0 0 0 5.9.1l8-8a4 4 0 0 0-.1-5.9", key: "t982x8" }],
  ["path", { d: "M21.3 6.3a2.5 2.5 0 0 0-3.5-3.5l-15 15a2.5 2.5 0 0 0 3.5 3.5Z", key: "rhan5l" }]
];

export { hotDog as default };
//# sourceMappingURL=hot-dog.js.map
