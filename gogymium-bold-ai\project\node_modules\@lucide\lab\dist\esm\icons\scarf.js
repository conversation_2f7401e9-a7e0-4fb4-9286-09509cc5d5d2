/**
 * @license @lucide/lab v0.1.2 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

const scarf = [
  [
    "path",
    {
      d: "M19.5 2.5 7 15c-.5.5-.6 1.5-.2 2L9 20 21.6 7.6a2 1.7 0 0 0 .1-1.9l-2-3c-.2-.4-.7-.7-1.2-.7h-13c-.5 0-1 .3-1.2.7l-2 3a2 1.7 0 0 0 .2 2l6 5.8",
      key: "ke54ui"
    }
  ],
  ["path", { d: "M12 10 4.5 2.5", key: "l2kccz" }],
  ["path", { d: "M13 20v2", key: "1t5i3p" }],
  ["path", { d: "M16 6H8", key: "whfohi" }],
  ["path", { d: "M17 12.1V22", key: "1sn4cd" }],
  ["path", { d: "M17 18h4", key: "xlnm2s" }],
  ["path", { d: "M17 20H9v2", key: "81fvye" }],
  ["path", { d: "M21 8.2V20", key: "pnvrlw" }]
];

export { scarf as default };
//# sourceMappingURL=scarf.js.map
