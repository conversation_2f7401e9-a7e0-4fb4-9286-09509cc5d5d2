{"author": "<PERSON> <<EMAIL>> (http://blog.izs.me/)", "name": "glob", "description": "the most correct and second fastest glob implementation in JavaScript", "version": "9.3.5", "repository": {"type": "git", "url": "git://github.com/isaacs/node-glob.git"}, "main": "./dist/cjs/index-cjs.js", "module": "./dist/mjs/index.js", "types": "./dist/mjs/index.d.ts", "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index-cjs.js"}}}, "files": ["dist"], "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "preprepare": "rm -rf dist", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "postprepare": "bash fixup.sh", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "c8 tap", "snap": "c8 tap", "format": "prettier --write . --loglevel warn", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "prepublish": "npm run benchclean", "profclean": "rm -f v8.log profile.txt", "test-regen": "npm run profclean && TEST_REGEN=1 node --no-warnings --loader ts-node/esm test/00-setup.ts", "prebench": "npm run prepare", "bench": "bash benchmark.sh", "preprof": "npm run prepare", "prof": "bash prof.sh", "benchclean": "node benchclean.js"}, "prettier": {"semi": false, "printWidth": 75, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "dependencies": {"fs.realpath": "^1.0.0", "minimatch": "^8.0.2", "minipass": "^4.2.4", "path-scurry": "^1.6.1"}, "devDependencies": {"@types/node": "^18.11.18", "@types/tap": "^15.0.7", "c8": "^7.12.0", "memfs": "^3.4.13", "mkdirp": "^2.1.4", "prettier": "^2.8.3", "rimraf": "^4.1.3", "tap": "^16.3.4", "ts-node": "^10.9.1", "typedoc": "^0.23.24", "typescript": "^4.9.4"}, "tap": {"before": "test/00-setup.ts", "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"], "ts": false}, "license": "ISC", "funding": {"url": "https://github.com/sponsors/isaacs"}, "engines": {"node": ">=16 || 14 >=14.17"}}