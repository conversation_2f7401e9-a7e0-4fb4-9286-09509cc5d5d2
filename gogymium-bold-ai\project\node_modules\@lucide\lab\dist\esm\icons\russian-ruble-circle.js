/**
 * @license @lucide/lab v0.1.2 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

const russianRubleCircle = [
  ["circle", { cx: "12", cy: "12", r: "10", key: "1mglay" }],
  ["path", { d: "M8 11h5a2 2 0 1 0 0-4h-3v10", key: "1usi5u" }],
  ["path", { d: "M8 15h5", key: "vxg57a" }]
];

export { russianRubleCircle as default };
//# sourceMappingURL=russian-ruble-circle.js.map
