/**
 * @license lucide-react-native v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

'use strict';

var createLucideIcon = require('../createLucideIcon.js');

const FileUser = createLucideIcon("FileUser", [
  ["path", { d: "M14 2v4a2 2 0 0 0 2 2h4", key: "tnqrlb" }],
  ["path", { d: "M15 18a3 3 0 1 0-6 0", key: "16awa0" }],
  ["path", { d: "M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7z", key: "1mlx9k" }],
  ["circle", { cx: "12", cy: "13", r: "2", key: "1c1ljs" }]
]);

module.exports = FileUser;
//# sourceMappingURL=file-user.js.map
