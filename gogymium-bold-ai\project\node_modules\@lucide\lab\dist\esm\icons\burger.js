/**
 * @license @lucide/lab v0.1.2 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

const burger = [
  [
    "path",
    {
      d: "M5 12a2 2 0 0 1-2-2 9 7 0 0 1 18 0 2 2 0 0 1-2 2l-3.5 4.1c-.8 1-2.4 1.1-3.4.3L7 12",
      key: "tuwmkq"
    }
  ],
  ["path", { d: "M11.7 16H4a2 2 0 0 1 0-4h16a2 2 0 0 1 0 4h-4.3", key: "66c14r" }],
  [
    "path",
    { d: "M5 16a2 2 0 0 0-2 2c0 1.7 1.3 3 3 3h12c1.7 0 3-1.3 3-3a2 2 0 0 0-2-2", key: "gmww6b" }
  ]
];

export { burger as default };
//# sourceMappingURL=burger.js.map
