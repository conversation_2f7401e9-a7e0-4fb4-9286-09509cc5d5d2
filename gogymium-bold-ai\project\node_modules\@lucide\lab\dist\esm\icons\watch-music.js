/**
 * @license @lucide/lab v0.1.2 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

const watchMusic = [
  ["path", { d: "m15.8 6-.5-2.4c-.2-1-1-1.6-2-1.6h-2.7a2 2 0 0 0-2 1.6L8.2 6", key: "1qkycp" }],
  ["rect", { width: "12", height: "12", x: "6", y: "6", rx: "2", key: "12p1cq" }],
  ["path", { d: "m8.2 18 .5 2.4c.2 1 1 1.6 2 1.6h2.7a2 2 0 0 0 2-1.6l.5-2.4", key: "e2n0tu" }],
  ["circle", { cx: "11.5", cy: "13.5", r: ".5", key: "1pw0ik" }],
  ["path", { d: "m14 11-2-1v3.5", key: "3rr27j" }]
];

export { watchMusic as default };
//# sourceMappingURL=watch-music.js.map
