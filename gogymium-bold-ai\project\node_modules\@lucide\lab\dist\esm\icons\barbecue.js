/**
 * @license @lucide/lab v0.1.2 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

const barbecue = [
  ["path", { d: "M6 4c0-1 2-1 2-2", key: "5f5awp" }],
  ["path", { d: "M12 4c0-1 2-1 2-2", key: "1rj9qk" }],
  ["path", { d: "M18 4c0-1 2-1 2-2", key: "d4wv5y" }],
  ["path", { d: "M3 8a9.06 9 0 0 0 18 0Z", key: "1j8zev" }],
  ["path", { d: "m9.2 15.6-1.3 2.6", key: "1f1g6q" }],
  ["circle", { cx: "7", cy: "20", r: "2", key: "1xhi4t" }],
  ["path", { d: "M9 20h8", key: "1l9aa7" }],
  ["path", { d: "M14.8 15.6 18 22", key: "1e6dn2" }]
];

export { barbecue as default };
//# sourceMappingURL=barbecue.js.map
