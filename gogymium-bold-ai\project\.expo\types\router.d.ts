/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/forgot-password`; params?: Router.UnknownInputParams; } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/login`; params?: Router.UnknownInputParams; } | { pathname: `/register`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/owner/camera` | `/owner/camera`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/owner/home` | `/owner/home`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/owner/members` | `/owner/members`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/owner/profile` | `/owner/profile`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/owner/qr-scanner` | `/owner/qr-scanner`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/user/clubs` | `/user/clubs`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/user/profile` | `/user/profile`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/user/subscription` | `/user/subscription`; params?: Router.UnknownInputParams; } | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/forgot-password`; params?: Router.UnknownOutputParams; } | { pathname: `/`; params?: Router.UnknownOutputParams; } | { pathname: `/login`; params?: Router.UnknownOutputParams; } | { pathname: `/register`; params?: Router.UnknownOutputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/owner/camera` | `/owner/camera`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/owner/home` | `/owner/home`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/owner/members` | `/owner/members`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/owner/profile` | `/owner/profile`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/owner/qr-scanner` | `/owner/qr-scanner`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/user/clubs` | `/user/clubs`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/user/profile` | `/user/profile`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/user/subscription` | `/user/subscription`; params?: Router.UnknownOutputParams; } | { pathname: `/+not-found`, params: Router.UnknownOutputParams & {  } };
      href: Router.RelativePathString | Router.ExternalPathString | `/forgot-password${`?${string}` | `#${string}` | ''}` | `/${`?${string}` | `#${string}` | ''}` | `/login${`?${string}` | `#${string}` | ''}` | `/register${`?${string}` | `#${string}` | ''}` | `/_sitemap${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/owner/camera${`?${string}` | `#${string}` | ''}` | `/owner/camera${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/owner/home${`?${string}` | `#${string}` | ''}` | `/owner/home${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/owner/members${`?${string}` | `#${string}` | ''}` | `/owner/members${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/owner/profile${`?${string}` | `#${string}` | ''}` | `/owner/profile${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/owner/qr-scanner${`?${string}` | `#${string}` | ''}` | `/owner/qr-scanner${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/user/clubs${`?${string}` | `#${string}` | ''}` | `/user/clubs${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/user/profile${`?${string}` | `#${string}` | ''}` | `/user/profile${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/user/subscription${`?${string}` | `#${string}` | ''}` | `/user/subscription${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/forgot-password`; params?: Router.UnknownInputParams; } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/login`; params?: Router.UnknownInputParams; } | { pathname: `/register`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/owner/camera` | `/owner/camera`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/owner/home` | `/owner/home`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/owner/members` | `/owner/members`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/owner/profile` | `/owner/profile`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/owner/qr-scanner` | `/owner/qr-scanner`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/user/clubs` | `/user/clubs`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/user/profile` | `/user/profile`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/user/subscription` | `/user/subscription`; params?: Router.UnknownInputParams; } | `/+not-found` | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } };
    }
  }
}
