/**
 * @license @lucide/lab v0.1.2 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

const farm = [
  ["path", { d: "M8 14V4.5a2.5 2.5 0 0 0-5 0V14", key: "e5czfd" }],
  ["path", { d: "m8 8 6-5 8 6", key: "8xm2nr" }],
  ["path", { d: "M20 4v10", key: "pxayuu" }],
  ["rect", { width: "4", height: "4", x: "12", y: "10", key: "6ksof3" }],
  ["path", { d: "M2 14h20", key: "myj16y" }],
  ["path", { d: "m2 22 5-8", key: "113mof" }],
  ["path", { d: "m7 22 5-8", key: "idcngg" }],
  ["path", { d: "M22 22H12l5-8", key: "sqpjgw" }],
  ["path", { d: "M15 18h7", key: "aq85id" }]
];

export { farm as default };
//# sourceMappingURL=farm.js.map
